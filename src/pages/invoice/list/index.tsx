import React, { useEffect, useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Tag,
  Link,
  Drawer,
  Descriptions,
  Divider,
  Tabs,
  Typography,
  Timeline,
  Modal,
} from '@arco-design/web-react';
import { IconSearch, IconPlus, IconCalendar, IconClockCircle, IconUser, IconDragDot, IconUnorderedList } from '@arco-design/web-react/icon';
import request from '@/utils/request';
import TabPane from '@arco-design/web-react/es/Tabs/tab-pane';
import TimelineItem from '@arco-design/web-react/es/Timeline/item';
import './style.module.less';

const { RangePicker } = DatePicker;

type DynamicObject = {
  [key: string]: any;
};

export default function InvoiceList() {
  const [data, setData] = useState([]);
  const [keyword, setKeyword] = useState('');
  const [infoVisible, setInfoVisible] = useState(false);
  const [rowSelection, setRowSelection] = useState<DynamicObject>({});
  const [previewVisible, setPreviewVisible] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
    pageSize: 10,
    current: 1,
  });

  const fetchData = () => {
    request
      .get('/order/list', { keyword, ...pagination })
      .then((res) => {
        setData(res.data.data);
        setPagination({
          ...pagination,
          total: res.data.total
        });
      })
      .catch((err) => {
        console.log(err);
      });
  };

  useEffect(() => {
    fetchData();
  }, [pagination.current]);

  const columns = [
    {
      title: '订单号',
      dataIndex: 'order_no',
      key: 'order_no',
    },
    {
      title: '发票类型',
      dataIndex: 'invoice',
      key: 'invoice_type',
      render: (invoice) => {
        const invoiceMap = {
          'normal': '普通发票',
          'special': '增值税专票',
        };

        return invoice ? invoiceMap[invoice.invoice_type] : '-';
      },
    },
    {
      title: '发票抬头',
      dataIndex: 'invoice',
      key: 'invoice_title',
      render: (invoice) => {
        return invoice ? invoice.title : '-';
      },
    },
    {
      title: '金额',
      dataIndex: 'formatted_total_amount',
      key: 'formatted_total_amount',
      render: (value) => `¥${value}`,
    },
    {
      title: '发票号码',
      dataIndex: 'invoice',
      key: 'invoice_no',
      render: (invoice) => {
        return invoice ? invoice.invoice_no : '-';
      },
    },
    {
      title: '开票日期',
      dataIndex: 'invoice',
      key: 'invoice_date',
      render: (invoice) => {
        return invoice ? invoice.invoice_date : '-';
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      render: (state) => {
        const stateMap = {
          success: { text: '已开票', color: 'green' },
          cancelled: { text: '已作废', color: 'red' },
          new: { text: '待处理', color: 'orange' },
        };
        const config = stateMap[state] || { text: state, color: 'gray' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 170,
      render: (_, record) => (
        <Space>
          <Link href='#' onClick={() => {
            console.log(record)
            setInfoVisible(true)
            setRowSelection(record)
          }}>
            详情
          </Link>
          <Link href='#' onClick={() => {
            setPreviewVisible(true)
            setRowSelection(record)
          }}>
            查看
          </Link>
          <Link href='#'>
            作废
          </Link>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Input
              placeholder="请输入发票号码"
              prefix={<IconSearch />}
              style={{ width: 200 }}
            />
            <Select
              placeholder="请选择状态"
              style={{ width: 120 }}
              options={[
                { label: '全部', value: 'all' },
                { label: '正常', value: 'normal' },
                { label: '已作废', value: 'cancelled' },
                { label: '待处理', value: 'pending' },
              ]}
            />
            <RangePicker placeholder={['开始日期', '结束日期']} />
            <Button type="primary" icon={<IconSearch />}>
              查询
            </Button>
            <Button icon={<IconPlus />}>新增发票</Button>
          </Space>
        </div>
        <Table
          rowKey='id'
          columns={columns}
          data={data}
          pagination={pagination}
        />
      </Card>
      {/* 详情抽屉 */}
      <Drawer
        width='50%'
        title='详情'
        visible={infoVisible}
        onOk={() => {
          setInfoVisible(false);
        }}
        onCancel={() => {
          setInfoVisible(false);
        }}
        footer={null}
      >
        <Descriptions
          colon=''
          title='单据详情'
          column={2}
          labelStyle={{ width: 60 }}
          data={[
            {
              label: '姓名',
              value: rowSelection?.name,
            },
            {
              label: '手机号',
              value: rowSelection?.phone,
            },
            {
              label: '单号',
              value: rowSelection?.order_no,
            },
            {
              label: '金额',
              value: `￥${rowSelection?.formatted_total_amount}`,
            },
            {
              label: '时间',
              value: rowSelection?.created_at,
            },
            {
              label: '状态',
              value: rowSelection?.state,
            },
          ]}
        />
        <Divider />
        <Tabs defaultActiveTab='1'>
          <TabPane
            key='1'
            title={
              <span>
                <IconCalendar style={{ marginRight: 6 }} />
                发票明细
              </span>
            }
          >
            <Typography.Paragraph>
              <Table
                rowKey='id'
                columns={[
                  {
                    title: '发票种类',
                    dataIndex: 'type',
                    key: 'type',
                  },
                  {
                    title: '发票类型',
                    dataIndex: 'invoice_type',
                    key: 'invoice_type',
                  },
                  {
                    title: '号码',
                    dataIndex: 'invoice_no',
                    key: 'invoice_no',
                  },
                  {
                    title: '操作',
                    key: 'action',
                    render: (_, record) => (
                      <Space>
                        <Link href='#'>
                          查看
                        </Link>
                        <Link href='#'>
                          下载
                        </Link>
                      </Space>
                    ),
                  }
                ]}
                data={rowSelection?.invoices}
                pagination={false}
                size='small'
                borderCell
                stripe
              />
            </Typography.Paragraph>
          </TabPane>
          <TabPane
            key='2'
            title={
              <span>
                <IconUnorderedList style={{ marginRight: 6 }} />
                订单明细
              </span>
            }
          >
            <Typography.Paragraph>
              <Table
                rowKey='id'
                columns={[
                  {
                    title: '商品名称',
                    dataIndex: 'article_name',
                    key: 'article_name',
                  },
                  {
                    title: '金额',
                    dataIndex: 'amount',
                    key: 'amount',
                  }
                ]}
                data={rowSelection?.detail}
                pagination={false}
                size='small'
                borderCell
                stripe
              />
            </Typography.Paragraph>
          </TabPane>
          <TabPane
            key='3'
            title={
              <span>
                <IconUser style={{ marginRight: 6 }} />
                操作记录
              </span>
            }
          >
            <Typography.Paragraph>
              <Timeline>
                <TimelineItem label='2017-03-10'>The first milestone</TimelineItem>
                <TimelineItem label='2018-05-12'>The second milestone</TimelineItem>
                <TimelineItem label='2020-09-30'>The third milestone</TimelineItem>
              </Timeline>
            </Typography.Paragraph>
          </TabPane>
        </Tabs>
      </Drawer>
      {/* 查看发票 */}
      <Modal
        title='Modal Title'
        visible={previewVisible}
        // style={{ width: '640px', height: '400px' }}
        className='invoice-preview-modal'
        footer={
          <>
            <Button
              onClick={() => {
                setPreviewVisible(false);
              }}
            >
              Return
            </Button>
            <Button
              onClick={() => {
                setPreviewVisible(false);
              }}
              type='primary'
            >
              Submit
            </Button>
          </>
        }
        onCancel={() => {
          setPreviewVisible(false);
        }}
      >
        <object data={'https://img.unionglasses.com/dzfp_25332000000281392802_%E6%9D%AD%E5%B7%9E%E5%AD%98%E6%B5%8E%E7%9C%BC%E9%95%9C%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8_20250630150733.pdf' + '#toolbar=0'} type="application/pdf">
          <p>PDF 加载失败，请<a href={rowSelection?.invoice?.invoice_url?.pdfUrl}>下载 PDF</a>查看。</p>
        </object>
      </Modal>
    </div>
  );
}
