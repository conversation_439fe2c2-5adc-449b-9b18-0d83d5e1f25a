// 发票预览模态框样式
:global(.invoice-preview-modal) {
  // 模态框整体样式
  .arco-modal {
    width: 80% !important;
    max-width: 1200px;
    height: 80vh;
  }

  .arco-modal-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .arco-modal-body {
    flex: 1;
    padding: 0;
    overflow: hidden;
  }

  // PDF预览容器
  object {
    width: 100%;
    height: 100%;
    border: none;
  }

  // 自定义子元素样式
  .preview-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .preview-header {
    padding: 16px;
    border-bottom: 1px solid var(--color-border-2);
    background-color: var(--color-bg-1);
  }

  .preview-body {
    flex: 1;
    overflow: auto;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .arco-modal {
      width: 95% !important;
      height: 90vh;
    }
  }
}
